const WebSocket = require('ws');
const { v4: uuidv4 } = require('uuid');
const Microphone = require('node-microphone');

class AliyunASR {
    constructor(appkey, token) {
        this.appkey = appkey;
        this.token = token;
        this.websocket = null;
        this.microphone = null;
        this.isRecording = false;
    }

    // 连接WebSocket
    connect() {
        const socketUrl = `wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1?token=${this.token}`;
        
        this.websocket = new WebSocket(socketUrl);

        this.websocket.on('open', () => {
            console.log('已连接到WebSocket服务器');

            // 发送开始转写请求
            const startTranscriptionMessage = {
                header: {
                    appkey: this.appkey,
                    namespace: "SpeechTranscriber",
                    name: "StartTranscription",
                    task_id: uuidv4().replace(/-/g, ''),
                    message_id: uuidv4().replace(/-/g, '')
                },
                payload: {
                    "format": "pcm",
                    "sample_rate": 16000,
                    "enable_intermediate_result": true,
                    "enable_punctuation_prediction": true,
                    "enable_inverse_text_normalization": true
                }
            };

            this.websocket.send(JSON.stringify(startTranscriptionMessage));
        });

        this.websocket.on('message', (data) => {
            const message = JSON.parse(data);
            console.log('收到服务器消息:', message);

            if (message.header.name === "TranscriptionStarted") {
                console.log('转写已开始，可以开始发送音频数据');
                this.startRecording();
            }
        });

        this.websocket.on('error', (error) => {
            console.error('WebSocket错误:', error);
        });

        this.websocket.on('close', () => {
            console.log('WebSocket连接已关闭');
            this.stopRecording();
        });
    }

    // 开始录音
    startRecording() {
        if (this.isRecording) {
            console.log('已经在录音中');
            return;
        }

        this.microphone = new Microphone({
            rate: 16000,
            channels: 1,
            debug: false,
            exitOnSilence: 6
        });

        const micStream = this.microphone.startRecording();
        this.isRecording = true;

        micStream.on('data', (data) => {
            if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                this.websocket.send(data);
            }
        });

        micStream.on('error', (error) => {
            console.error('录音错误:', error);
        });

        console.log('开始录音');
    }

    // 停止录音
    stopRecording() {
        if (!this.isRecording) {
            console.log('没有正在进行的录音');
            return;
        }

        if (this.microphone) {
            this.microphone.stopRecording();
            this.isRecording = false;
            console.log('停止录音');
        }
    }

    // 断开连接
    disconnect() {
        if (this.websocket) {
            this.websocket.close();
        }
        this.stopRecording();
    }
}

// 使用示例
const appkey = 'your_appkey';
const token = 'your_token';

const asr = new AliyunASR(appkey, token);

// 连接WebSocket
asr.connect();

// 处理程序退出
process.on('SIGINT', () => {
    console.log('正在关闭程序...');
    asr.disconnect();
    process.exit();
});